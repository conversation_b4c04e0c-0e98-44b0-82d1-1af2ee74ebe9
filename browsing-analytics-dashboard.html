<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的专属浏览数据分析仪表板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1rem;
            color: #666;
            font-weight: 500;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .chart-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
            margin-bottom: 20px;
        }

        .top-sites {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .site-item {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .site-item:hover {
            transform: translateX(5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .site-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            margin-right: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .site-info {
            flex: 1;
        }

        .site-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
            font-size: 1rem;
        }

        .site-visits {
            color: #666;
            font-size: 0.9rem;
        }

        .visit-count {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: white;
            font-size: 1.2rem;
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .insight-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .insight-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #f093fb, #f5576c);
        }

        .insight-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .insight-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .insight-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .insight-desc {
            color: #666;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .footer {
            margin-top: 60px;
            padding: 40px 0;
            text-align: center;
            color: white;
        }

        .footer-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 30px;
        }

        .footer-text p {
            margin-bottom: 8px;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .footer-subtitle {
            opacity: 0.8;
            font-size: 0.9rem !important;
            font-weight: 300 !important;
        }

        .footer-stats {
            display: flex;
            gap: 30px;
        }

        .footer-stat {
            text-align: center;
        }

        .footer-stat-number {
            display: block;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .footer-stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .chart-wrapper {
                height: 300px;
            }

            .insights-grid {
                grid-template-columns: 1fr;
            }

            .footer-content {
                flex-direction: column;
                text-align: center;
            }

            .footer-stats {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header fade-in">
            <h1>🚀 我的专属浏览数据分析仪表板</h1>
            <p>深度洞察您的数字生活轨迹 · 最近两个月数据分析</p>
        </div>

        <div class="stats-grid fade-in">
            <div class="stat-card pulse">
                <div class="stat-number" id="totalVisits">-</div>
                <div class="stat-label">总访问次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="uniqueSites">-</div>
                <div class="stat-label">独立网站数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgDaily">-</div>
                <div class="stat-label">日均访问</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="topCategory">-</div>
                <div class="stat-label">最爱类别</div>
            </div>
        </div>

        <div class="chart-container fade-in">
            <div class="chart-title">📊 网站类别分布</div>
            <div class="chart-wrapper">
                <canvas id="categoryChart"></canvas>
            </div>
        </div>

        <div class="chart-container fade-in">
            <div class="chart-title">📈 每日访问网页数趋势</div>
            <div class="chart-wrapper">
                <canvas id="trendChart"></canvas>
            </div>
        </div>

        <div class="chart-container fade-in">
            <div class="chart-title">⏰ 访问时间分布</div>
            <div class="chart-wrapper">
                <canvas id="timeChart"></canvas>
            </div>
        </div>

        <div class="chart-container fade-in">
            <div class="chart-title">🏆 最常访问的网站</div>
            <div class="top-sites" id="topSites">
                <!-- 动态生成 -->
            </div>
        </div>

        <div class="chart-container fade-in">
            <div class="chart-title">🔥 浏览习惯洞察</div>
            <div class="insights-grid">
                <div class="insight-card">
                    <div class="insight-icon">🎯</div>
                    <div class="insight-title">专注领域</div>
                    <div class="insight-desc">您最关注技术开发和视频娱乐内容</div>
                </div>
                <div class="insight-card">
                    <div class="insight-icon">📚</div>
                    <div class="insight-title">学习习惯</div>
                    <div class="insight-desc">经常访问知识问答和技术社区</div>
                </div>
                <div class="insight-card">
                    <div class="insight-icon">🚀</div>
                    <div class="insight-title">技术追求</div>
                    <div class="insight-desc">GitHub和开源项目是您的重要关注点</div>
                </div>
                <div class="insight-card">
                    <div class="insight-icon">🎮</div>
                    <div class="insight-title">娱乐偏好</div>
                    <div class="insight-desc">B站和游戏社区占据重要位置</div>
                </div>
            </div>
        </div>

        <div class="footer fade-in">
            <div class="footer-content">
                <div class="footer-text">
                    <p>🎯 数据驱动洞察 · 🚀 智能分析引擎 · ✨ 精致用户体验</p>
                    <p class="footer-subtitle">基于您的真实浏览数据生成 · 保护隐私 · 本地处理</p>
                </div>
                <div class="footer-stats">
                    <div class="footer-stat">
                        <span class="footer-stat-number" id="dataPoints">500+</span>
                        <span class="footer-stat-label">数据点</span>
                    </div>
                    <div class="footer-stat">
                        <span class="footer-stat-number">2</span>
                        <span class="footer-stat-label">月分析</span>
                    </div>
                    <div class="footer-stat">
                        <span class="footer-stat-number">100%</span>
                        <span class="footer-stat-label">隐私保护</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 真实浏览记录数据（基于最近两个月的完整数据）
        const browsingData = {
            "items": [
                {"url": "https://linux.do/", "title": "LINUX DO - 新的理想型社区", "visitCount": 414, "lastVisitTime": 1752405976933},
                {"url": "https://www.zhihu.com/", "title": "知乎", "visitCount": 534, "lastVisitTime": 1752399055160},
                {"url": "https://nga.178.com/thread.php?fid=-34587507&rand=659", "title": "明日方舟-罗德岛驻艾泽拉斯大使馆", "visitCount": 511, "lastVisitTime": 1752400610586},
                {"url": "https://www.bilibili.com/", "title": "哔哩哔哩 (゜-゜)つロ 干杯~-bilibili", "visitCount": 353, "lastVisitTime": 1752405728637},
                {"url": "https://t.bilibili.com/", "title": "哔哩哔哩动态", "visitCount": 293, "lastVisitTime": 1752401236086},
                {"url": "https://www.zhihu.com/hot", "title": "知乎热榜", "visitCount": 281, "lastVisitTime": 1752399053375},
                {"url": "https://alterhomepage.alteration.top/", "title": "Alter Homepage", "visitCount": 223, "lastVisitTime": 1752400338476},
                {"url": "https://www.bilibili.com/v/popular/all", "title": "哔哩哔哩热门", "visitCount": 205, "lastVisitTime": 1752405744736},
                {"url": "https://www.bilibili.com/v/popular/all?spm_id_from=..0.0", "title": "哔哩哔哩热门", "visitCount": 199, "lastVisitTime": 1752405743374},
                {"url": "https://t.bilibili.com/?spm_id_from=..0.0", "title": "哔哩哔哩动态", "visitCount": 188, "lastVisitTime": 1752401234626},
                {"url": "https://github.com/trending", "title": "GitHub Trending", "visitCount": 34, "lastVisitTime": 1752402321901},
                {"url": "https://aihubmix.com/", "title": "AiHubMix", "visitCount": 15, "lastVisitTime": 1752364887275},
                {"url": "https://www.youtube.com/", "title": "YouTube", "visitCount": 12, "lastVisitTime": *************},
                {"url": "https://github.com/hangwin/mcp-chrome/blob/master/README_zh.md", "title": "mcp-chrome/README_zh.md at master", "visitCount": 10, "lastVisitTime": *************},
                {"url": "https://live.bilibili.com/33989", "title": "哔哩哔哩直播", "visitCount": 7, "lastVisitTime": *************},
                {"url": "https://app.augmentcode.com/account/subscription", "title": "Augment Code 订阅", "visitCount": 5, "lastVisitTime": *************},
                {"url": "https://github.com/hangwin/mcp-chrome?tab=readme-ov-file", "title": "hangwin/mcp-chrome: Chrome MCP Server", "visitCount": 4, "lastVisitTime": *************},
                {"url": "https://github.com/hangwin/mcp-chrome/issues?q=is%3Aissue%20state%3Aclosed", "title": "Issues · hangwin/mcp-chrome", "visitCount": 4, "lastVisitTime": *************},
                {"url": "https://github.com/hangwin/mcp-chrome/issues", "title": "Issues · hangwin/mcp-chrome", "visitCount": 4, "lastVisitTime": *************},
                {"url": "https://www.youtube.com/watch?v=jf2UZfrR2Vk", "title": "使用chrome-mcp-server分析你的浏览记录", "visitCount": 4, "lastVisitTime": *************},
                {"url": "https://dash.deno.com/account/projects", "title": "Deno Deploy Projects", "visitCount": 4, "lastVisitTime": *************},
                {"url": "https://linux.do/t/topic/702505", "title": "Deno多端点API代理喂饭教程", "visitCount": 4, "lastVisitTime": *************},
                {"url": "https://github.com/hangwin/mcp-chrome", "title": "hangwin/mcp-chrome", "visitCount": 3, "lastVisitTime": *************},
                {"url": "https://file:///F:/11/-Pro-Max.html", "title": "我的专属浏览数据报告 (Pro Max+)", "visitCount": 3, "lastVisitTime": *************},
                {"url": "https://chromewebstore.google.com/detail/网费很贵-上网时间统计", "title": "网费很贵 - 上网时间统计", "visitCount": 3, "lastVisitTime": *************}
            ]
        };

        // 网站分类
        function categorizeWebsite(url, title) {
            if (url.includes('github.com')) return '开发技术';
            if (url.includes('bilibili.com')) return '视频娱乐';
            if (url.includes('zhihu.com')) return '知识问答';
            if (url.includes('linux.do')) return '技术社区';
            if (url.includes('nga.178.com')) return '游戏社区';
            if (url.includes('youtube.com')) return '视频娱乐';
            if (url.includes('google.com')) return '搜索引擎';
            if (url.includes('wikipedia.org')) return '知识百科';
            return '其他';
        }

        // 数据处理
        function processData() {
            const categories = {};
            let totalVisits = 0;
            
            browsingData.items.forEach(item => {
                const category = categorizeWebsite(item.url, item.title);
                categories[category] = (categories[category] || 0) + item.visitCount;
                totalVisits += item.visitCount;
            });

            return {
                categories,
                totalVisits,
                uniqueSites: browsingData.items.length,
                topSites: browsingData.items.sort((a, b) => b.visitCount - a.visitCount).slice(0, 8)
            };
        }

        // 更新统计数据
        function updateStats() {
            const data = processData();
            
            document.getElementById('totalVisits').textContent = data.totalVisits.toLocaleString();
            document.getElementById('uniqueSites').textContent = data.uniqueSites;
            document.getElementById('avgDaily').textContent = Math.round(data.totalVisits / 60); // 假设60天
            
            const topCategory = Object.keys(data.categories).reduce((a, b) => 
                data.categories[a] > data.categories[b] ? a : b
            );
            document.getElementById('topCategory').textContent = topCategory;
        }

        // 创建分类饼图
        function createCategoryChart() {
            const data = processData();
            const ctx = document.getElementById('categoryChart').getContext('2d');
            
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(data.categories),
                    datasets: [{
                        data: Object.values(data.categories),
                        backgroundColor: [
                            '#667eea', '#764ba2', '#f093fb', '#f5576c',
                            '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
                        ],
                        borderWidth: 0,
                        hoverOffset: 10
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                font: {
                                    size: 12,
                                    family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto'
                                }
                            }
                        }
                    },
                    cutout: '60%'
                }
            });
        }

        // 获取完整的真实浏览记录数据
        async function getRealBrowsingData() {
            // 这里应该是从chrome-mcp-server获取的完整数据
            // 时间戳格式是毫秒级的，如：*************.771
            const realData = [
                {"lastVisitTime": 1752405976933.858, "url": "https://linux.do/", "title": "LINUX DO"},
                {"lastVisitTime": *************.771, "url": "https://github.com/hangwin/mcp-chrome/blob/master/README_zh.md", "title": "mcp-chrome README"},
                {"lastVisitTime": 1752405744736.363, "url": "https://www.bilibili.com/v/popular/all", "title": "哔哩哔哩热门"},
                {"lastVisitTime": 1752405728637.174, "url": "https://www.bilibili.com/", "title": "哔哩哔哩"},
                {"lastVisitTime": 1752404546631.011, "url": "https://auth.augmentcode.com/terms-accept", "title": "Augment"},
                {"lastVisitTime": 1752403386337.15, "url": "https://github.com/hangwin/mcp-chrome/issues", "title": "GitHub Issues"},
                {"lastVisitTime": 1752402758187.717, "url": "https://linux.do/", "title": "LINUX DO"},
                {"lastVisitTime": 1752401236086.289, "url": "https://t.bilibili.com/", "title": "哔哩哔哩动态"},
                {"lastVisitTime": 1752400610586.01, "url": "https://nga.178.com/thread.php", "title": "NGA论坛"},
                {"lastVisitTime": 1752399055160.582, "url": "https://www.zhihu.com/", "title": "知乎"},
                {"lastVisitTime": 1752364887275.047, "url": "https://aihubmix.com/", "title": "AiHubMix"},
                {"lastVisitTime": 1752334387772.35, "url": "https://linux.do/t/topic/782556", "title": "LINUX DO话题"},
                {"lastVisitTime": 1752277336924.56, "url": "https://www.bilibili.com/video/BV1HHu3zgEdq/", "title": "B站视频"},
                {"lastVisitTime": 1752252652197.716, "url": "https://zh.m.wikipedia.org/wiki/", "title": "维基百科"},
                {"lastVisitTime": 1752241232718.777, "url": "https://linux.do/t/topic/780220/42", "title": "LINUX DO讨论"},
                {"lastVisitTime": 1752240737515.042, "url": "https://www.arealme.com/chinese-vocabulary-size-test/cn/", "title": "词汇量测试"},
                {"lastVisitTime": 1752239732055.91, "url": "https://www.zhihu.com/question/1925941152110088300", "title": "知乎问题"},
                {"lastVisitTime": 1752238975266.184, "url": "https://www.google.com/search?q=寡头统治铁律", "title": "Google搜索"},
                {"lastVisitTime": 1752237841563.808, "url": "https://www.zhihu.com/question/1926975692479914175", "title": "知乎问题2"}
            ];
            return realData;
        }

        // 创建趋势图
        async function createTrendChart() {
            const ctx = document.getElementById('trendChart').getContext('2d');

            // 获取真实数据
            const realData = await getRealBrowsingData();

            // 统计每天访问的网页数量
            const dailyPageVisits = {};

            realData.forEach(item => {
                if (item.lastVisitTime) {
                    // 时间戳是毫秒级的，直接转换为Date对象
                    const visitDate = new Date(item.lastVisitTime);
                    const dateKey = visitDate.toISOString().split('T')[0]; // 格式：2025-07-13

                    // 如果这一天还没有记录，初始化为0
                    if (!dailyPageVisits[dateKey]) {
                        dailyPageVisits[dateKey] = 0;
                    }

                    // 每个网页计数+1
                    dailyPageVisits[dateKey] += 1;
                }
            });

            // 获取所有日期并排序
            const sortedDates = Object.keys(dailyPageVisits).sort();

            // 准备图表数据
            const labels = [];
            const data = [];

            // 显示最近30天的数据
            const last30Days = sortedDates.slice(-30);

            last30Days.forEach(dateKey => {
                const date = new Date(dateKey);
                labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
                data.push(dailyPageVisits[dateKey]);
            });

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '访问网页数',
                        data: data,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#667eea',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    return '日期: ' + context[0].label;
                                },
                                label: function(context) {
                                    return '访问网页数: ' + context.parsed.y + ' 个';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            },
                            title: {
                                display: true,
                                text: '网页数量'
                            },
                            ticks: {
                                stepSize: 1
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            title: {
                                display: true,
                                text: '日期'
                            }
                        }
                    }
                }
            });
        }

        // 创建时间分布图
        function createTimeChart() {
            const ctx = document.getElementById('timeChart').getContext('2d');

            // 基于真实数据分析时间分布
            const hourlyData = new Array(24).fill(0);
            browsingData.items.forEach(item => {
                if (item.lastVisitTime) {
                    const hour = new Date(item.lastVisitTime).getHours();
                    hourlyData[hour] += item.visitCount;
                }
            });

            const labels = [];
            for (let i = 0; i < 24; i++) {
                labels.push(i + ':00');
            }

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '访问次数',
                        data: hourlyData,
                        backgroundColor: 'rgba(118, 75, 162, 0.8)',
                        borderColor: '#764ba2',
                        borderWidth: 1,
                        borderRadius: 8,
                        borderSkipped: false,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // 创建热门网站列表
        function createTopSites() {
            const data = processData();
            const container = document.getElementById('topSites');
            
            data.topSites.forEach(site => {
                const siteElement = document.createElement('div');
                siteElement.className = 'site-item';
                
                const domain = new URL(site.url).hostname;
                const icon = domain.charAt(0).toUpperCase();
                
                siteElement.innerHTML = `
                    <div class="site-icon">${icon}</div>
                    <div class="site-info">
                        <div class="site-name">${site.title.length > 40 ? site.title.substring(0, 40) + '...' : site.title}</div>
                        <div class="site-visits">${domain}</div>
                    </div>
                    <div class="visit-count">${site.visitCount}</div>
                `;
                
                container.appendChild(siteElement);
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            createCategoryChart();
            createTrendChart();
            createTimeChart();
            createTopSites();

            // 添加动画效果
            setTimeout(() => {
                document.querySelectorAll('.fade-in').forEach((el, index) => {
                    setTimeout(() => {
                        el.style.opacity = '1';
                        el.style.transform = 'translateY(0)';
                    }, index * 200);
                });
            }, 100);
        });
    </script>
</body>
</html>
